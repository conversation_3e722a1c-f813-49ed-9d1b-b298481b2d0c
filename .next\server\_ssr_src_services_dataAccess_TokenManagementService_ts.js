"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_services_dataAccess_TokenManagementService_ts";
exports.ids = ["_ssr_src_services_dataAccess_TokenManagementService_ts"];
exports.modules = {

/***/ "(ssr)/./src/services/dataAccess/TokenManagementService.ts":
/*!***********************************************************!*\
  !*** ./src/services/dataAccess/TokenManagementService.ts ***!
  \***********************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TokenManagementService: () => (/* binding */ TokenManagementService),\n/* harmony export */   getJWTConfig: () => (/* binding */ getJWTConfig),\n/* harmony export */   tokenManagementService: () => (/* binding */ tokenManagementService)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(ssr)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * 统一Token管理服务\n * \n * 符合DataAccessManager架构规范的Token管理实现\n * 集成缓存、性能监控和错误处理\n */ \n/**\n * JWT配置\n */ const JWT_CONFIG = {\n    accessToken: {\n        secret: process.env.JWT_ACCESS_SECRET || \"default-access-secret-change-in-production\",\n        expiresIn: \"1h\",\n        algorithm: \"HS256\"\n    },\n    refreshToken: {\n        secret: process.env.JWT_REFRESH_SECRET || \"default-refresh-secret-change-in-production\",\n        expiresIn: \"7d\",\n        algorithm: \"HS256\"\n    }\n};\n/**\n * 统一Token管理服务实现\n * 符合DataAccessManager单一入口架构\n */ class TokenManagementService {\n    constructor(){\n        console.log(\"\\uD83D\\uDD27 [TokenManagementService] 服务实例已创建\");\n    }\n    /**\n   * 获取单例实例\n   */ static getInstance() {\n        if (!TokenManagementService.instance) {\n            TokenManagementService.instance = new TokenManagementService();\n        }\n        return TokenManagementService.instance;\n    }\n    /**\n   * 检查运行环境\n   */ checkServerEnvironment() {\n        if (false) {}\n    }\n    /**\n   * 生成Access Token\n   */ async generateAccessToken(payload) {\n        this.checkServerEnvironment();\n        try {\n            console.log(\"\\uD83D\\uDE80 [TokenManagementService] 开始生成Access Token\");\n            const tokenPayload = {\n                ...payload,\n                iat: Math.floor(Date.now() / 1000),\n                exp: Math.floor(Date.now() / 1000) + 60 * 60 // 1小时后过期\n            };\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(tokenPayload, JWT_CONFIG.accessToken.secret, {\n                algorithm: JWT_CONFIG.accessToken.algorithm,\n                expiresIn: JWT_CONFIG.accessToken.expiresIn\n            });\n            console.log(\"✅ [TokenManagementService] Access Token生成成功\");\n            return token;\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 [TokenManagementService] 生成Access Token失败:\", error);\n            throw new Error(\"Token生成失败\");\n        }\n    }\n    /**\n   * 生成Refresh Token\n   */ async generateRefreshToken(userId, sessionId) {\n        this.checkServerEnvironment();\n        try {\n            console.log(\"\\uD83D\\uDE80 [TokenManagementService] 开始生成Refresh Token\");\n            const payload = {\n                userId,\n                sessionId,\n                type: \"refresh\",\n                iat: Math.floor(Date.now() / 1000)\n            };\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().sign(payload, JWT_CONFIG.refreshToken.secret, {\n                algorithm: JWT_CONFIG.refreshToken.algorithm,\n                expiresIn: JWT_CONFIG.refreshToken.expiresIn\n            });\n            console.log(\"✅ [TokenManagementService] Refresh Token生成成功\");\n            return token;\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 [TokenManagementService] 生成Refresh Token失败:\", error);\n            throw new Error(\"Refresh Token生成失败\");\n        }\n    }\n    /**\n   * 验证Access Token\n   */ async verifyAccessToken(token) {\n        this.checkServerEnvironment();\n        try {\n            if (!token) {\n                return {\n                    isValid: false,\n                    error: \"Token不能为空\"\n                };\n            }\n            const payload = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_CONFIG.accessToken.secret);\n            // 检查Token是否过期\n            const now = Math.floor(Date.now() / 1000);\n            if (payload.exp && payload.exp < now) {\n                return {\n                    isValid: false,\n                    error: \"Token已过期\",\n                    isExpired: true\n                };\n            }\n            return {\n                isValid: true,\n                payload\n            };\n        } catch (error) {\n            if (error && typeof error === \"object\" && \"name\" in error) {\n                if (error.name === \"TokenExpiredError\") {\n                    return {\n                        isValid: false,\n                        error: \"Token已过期\",\n                        isExpired: true\n                    };\n                } else if (error.name === \"JsonWebTokenError\") {\n                    return {\n                        isValid: false,\n                        error: \"Token格式无效\"\n                    };\n                }\n            }\n            return {\n                isValid: false,\n                error: \"Token验证失败\"\n            };\n        }\n    }\n    /**\n   * 验证Refresh Token\n   */ async verifyRefreshToken(token) {\n        this.checkServerEnvironment();\n        try {\n            if (!token) {\n                return {\n                    isValid: false,\n                    error: \"Refresh Token不能为空\"\n                };\n            }\n            const payload = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default().verify(token, JWT_CONFIG.refreshToken.secret);\n            if (payload.type !== \"refresh\") {\n                return {\n                    isValid: false,\n                    error: \"Token类型无效\"\n                };\n            }\n            return {\n                isValid: true,\n                userId: payload.userId,\n                sessionId: payload.sessionId\n            };\n        } catch (error) {\n            if (error && typeof error === \"object\" && \"name\" in error) {\n                if (error.name === \"TokenExpiredError\") {\n                    return {\n                        isValid: false,\n                        error: \"Refresh Token已过期\"\n                    };\n                } else if (error.name === \"JsonWebTokenError\") {\n                    return {\n                        isValid: false,\n                        error: \"Refresh Token格式无效\"\n                    };\n                }\n            }\n            return {\n                isValid: false,\n                error: \"Refresh Token验证失败\"\n            };\n        }\n    }\n    /**\n   * 从Token中提取用户信息\n   */ async extractUserInfo(token) {\n        try {\n            const validation = await this.verifyAccessToken(token);\n            if (!validation.isValid || !validation.payload) {\n                return null;\n            }\n            const { userId, username, roles, permissions } = validation.payload;\n            return {\n                userId,\n                username,\n                roles,\n                permissions\n            };\n        } catch (error) {\n            return null;\n        }\n    }\n    /**\n   * 检查Token是否即将过期\n   */ async shouldRefreshToken(token, thresholdMinutes = 5) {\n        try {\n            const validation = await this.verifyAccessToken(token);\n            if (!validation.isValid || !validation.payload) {\n                return false;\n            }\n            const now = Math.floor(Date.now() / 1000);\n            const threshold = thresholdMinutes * 60;\n            return validation.payload.exp - now <= threshold;\n        } catch (error) {\n            return false;\n        }\n    }\n    /**\n   * 获取Token剩余有效时间（秒）\n   */ async getTokenRemainingTime(token) {\n        try {\n            const validation = await this.verifyAccessToken(token);\n            if (!validation.isValid || !validation.payload) {\n                return -1;\n            }\n            const now = Math.floor(Date.now() / 1000);\n            return Math.max(0, validation.payload.exp - now);\n        } catch (error) {\n            return -1;\n        }\n    }\n    /**\n   * 生成会话ID\n   */ generateSessionId() {\n        return `session-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`;\n    }\n    /**\n   * 检查环境变量配置\n   */ checkConfiguration() {\n        const warnings = [];\n        if (JWT_CONFIG.accessToken.secret === \"default-access-secret-change-in-production\") {\n            warnings.push(\"使用默认的Access Token密钥，生产环境中请设置JWT_ACCESS_SECRET环境变量\");\n        }\n        if (JWT_CONFIG.refreshToken.secret === \"default-refresh-secret-change-in-production\") {\n            warnings.push(\"使用默认的Refresh Token密钥，生产环境中请设置JWT_REFRESH_SECRET环境变量\");\n        }\n        if (JWT_CONFIG.accessToken.secret === JWT_CONFIG.refreshToken.secret) {\n            warnings.push(\"Access Token和Refresh Token使用相同密钥，建议使用不同的密钥\");\n        }\n        return {\n            isValid: warnings.length === 0,\n            warnings\n        };\n    }\n}\n/**\n * 导出单例实例\n */ const tokenManagementService = TokenManagementService.getInstance();\n/**\n * 导出JWT配置（只读）\n */ const getJWTConfig = ()=>({\n        accessTokenExpiresIn: JWT_CONFIG.accessToken.expiresIn,\n        refreshTokenExpiresIn: JWT_CONFIG.refreshToken.expiresIn,\n        algorithm: JWT_CONFIG.accessToken.algorithm\n    });\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/services/dataAccess/TokenManagementService.ts\n");

/***/ })

};
;