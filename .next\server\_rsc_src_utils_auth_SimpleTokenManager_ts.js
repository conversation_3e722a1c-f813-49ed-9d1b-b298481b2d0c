"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_rsc_src_utils_auth_SimpleTokenManager_ts";
exports.ids = ["_rsc_src_utils_auth_SimpleTokenManager_ts"];
exports.modules = {

/***/ "(rsc)/./src/utils/auth/SimpleTokenManager.ts":
/*!**********************************************!*\
  !*** ./src/utils/auth/SimpleTokenManager.ts ***!
  \**********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SimpleTokenManager: () => (/* binding */ SimpleTokenManager)\n/* harmony export */ });\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! jsonwebtoken */ \"(rsc)/./node_modules/jsonwebtoken/index.js\");\n/* harmony import */ var jsonwebtoken__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n/**\n * 简化的JWT Token管理器\n * 专门用于解决jsonwebtoken库的环境兼容性问题\n */ \nconst JWT_SECRET = process.env.JWT_ACCESS_SECRET || \"default-secret-key-for-development\";\nconst JWT_EXPIRES_IN = \"1h\";\n/**\n * 简化的Token管理器\n */ class SimpleTokenManager {\n    /**\n   * 生成Access Token（简化版）\n   */ static generateAccessToken(payload) {\n        console.log(\"\\uD83D\\uDE80 [SimpleTokenManager] 开始生成Token\");\n        console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] 载荷数据:\", JSON.stringify(payload));\n        try {\n            // 检查运行环境\n            const isServer = \"undefined\" === \"undefined\";\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] 运行环境:\", isServer ? \"服务器\" : \"浏览器\");\n            if (!isServer) {\n                console.error(\"❌ [SimpleTokenManager] 不能在浏览器环境中运行\");\n                throw new Error(\"Token生成必须在服务器端进行\");\n            }\n            // 检查jwt库\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] jwt库类型:\", typeof jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__);\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] jwt.sign存在:\", typeof jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.sign === \"function\");\n            const tokenPayload = {\n                ...payload,\n                iat: Math.floor(Date.now() / 1000),\n                exp: Math.floor(Date.now() / 1000) + 3600 // 1小时后过期\n            };\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] 完整载荷:\", JSON.stringify(tokenPayload));\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] 密钥长度:\", JWT_SECRET.length);\n            // 直接使用jwt.sign，不使用额外选项\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.sign(tokenPayload, JWT_SECRET);\n            console.log(\"✅ [SimpleTokenManager] Token生成成功，长度:\", token.length);\n            return token;\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 [SimpleTokenManager] Token生成失败:\", error);\n            console.error(\"\\uD83D\\uDCA5 [SimpleTokenManager] 错误详情:\", {\n                name: error instanceof Error ? error.name : \"Unknown\",\n                message: error instanceof Error ? error.message : String(error),\n                stack: error instanceof Error ? error.stack : undefined\n            });\n            throw new Error(`Token生成失败: ${error instanceof Error ? error.message : String(error)}`);\n        }\n    }\n    /**\n   * 生成Refresh Token（简化版）\n   */ static generateRefreshToken(userId, sessionId) {\n        console.log(\"\\uD83D\\uDE80 [SimpleTokenManager] 开始生成Refresh Token\");\n        try {\n            const payload = {\n                userId,\n                sessionId,\n                type: \"refresh\",\n                iat: Math.floor(Date.now() / 1000),\n                exp: Math.floor(Date.now() / 1000) + 7 * 24 * 3600 // 7天后过期\n            };\n            const token = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.sign(payload, JWT_SECRET);\n            console.log(\"✅ [SimpleTokenManager] Refresh Token生成成功\");\n            return token;\n        } catch (error) {\n            console.error(\"\\uD83D\\uDCA5 [SimpleTokenManager] Refresh Token生成失败:\", error);\n            throw new Error(`Refresh Token生成失败: ${error instanceof Error ? error.message : String(error)}`);\n        }\n    }\n    /**\n   * 验证Access Token\n   */ static verifyAccessToken(token) {\n        try {\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] 开始验证Access Token\");\n            // 检查运行环境\n            const isServer = \"undefined\" === \"undefined\";\n            if (!isServer) {\n                console.error(\"❌ [SimpleTokenManager] Token验证必须在服务器端进行\");\n                throw new Error(\"Token验证必须在服务器端进行\");\n            }\n            // 验证Token\n            jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.verify(token, JWT_SECRET);\n            console.log(\"✅ [SimpleTokenManager] Access Token验证成功\");\n            return true;\n        } catch (error) {\n            console.log(\"❌ [SimpleTokenManager] Access Token验证失败:\", error instanceof Error ? error.message : String(error));\n            return false;\n        }\n    }\n    /**\n   * 验证Refresh Token\n   */ static verifyRefreshToken(token) {\n        try {\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] 开始验证Refresh Token\");\n            const isServer = \"undefined\" === \"undefined\";\n            if (!isServer) {\n                console.error(\"❌ [SimpleTokenManager] Token验证必须在服务器端进行\");\n                throw new Error(\"Token验证必须在服务器端进行\");\n            }\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.verify(token, JWT_SECRET);\n            // 检查是否是refresh token类型\n            if (decoded.type !== \"refresh\") {\n                console.log(\"❌ [SimpleTokenManager] 不是有效的Refresh Token类型\");\n                return false;\n            }\n            console.log(\"✅ [SimpleTokenManager] Refresh Token验证成功\");\n            return true;\n        } catch (error) {\n            console.log(\"❌ [SimpleTokenManager] Refresh Token验证失败:\", error instanceof Error ? error.message : String(error));\n            return false;\n        }\n    }\n    /**\n   * 解析Token获取payload（不验证签名）\n   */ static parseToken(token) {\n        try {\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] 开始解析Token\");\n            const parts = token.split(\".\");\n            if (parts.length !== 3) {\n                console.log(\"❌ [SimpleTokenManager] Token格式无效\");\n                return null;\n            }\n            // 解析payload部分\n            const payload = JSON.parse(Buffer.from(parts[1], \"base64\").toString());\n            console.log(\"✅ [SimpleTokenManager] Token解析成功\");\n            return payload;\n        } catch (error) {\n            console.log(\"❌ [SimpleTokenManager] Token解析失败:\", error instanceof Error ? error.message : String(error));\n            return null;\n        }\n    }\n    /**\n   * 解析并验证Token获取payload\n   */ static verifyAndParseToken(token) {\n        try {\n            console.log(\"\\uD83D\\uDD0D [SimpleTokenManager] 开始验证并解析Token\");\n            const isServer = \"undefined\" === \"undefined\";\n            if (!isServer) {\n                console.error(\"❌ [SimpleTokenManager] Token验证必须在服务器端进行\");\n                throw new Error(\"Token验证必须在服务器端进行\");\n            }\n            const decoded = jsonwebtoken__WEBPACK_IMPORTED_MODULE_0__.verify(token, JWT_SECRET);\n            console.log(\"✅ [SimpleTokenManager] Token验证并解析成功\");\n            return decoded;\n        } catch (error) {\n            console.log(\"❌ [SimpleTokenManager] Token验证并解析失败:\", error instanceof Error ? error.message : String(error));\n            return null;\n        }\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/utils/auth/SimpleTokenManager.ts\n");

/***/ })

};
;